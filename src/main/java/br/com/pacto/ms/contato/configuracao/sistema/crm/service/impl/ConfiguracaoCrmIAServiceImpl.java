package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.EmpresaEntity;
import br.com.pacto.ms.contato.avulso.data.domain.UsuarioEntity;
import br.com.pacto.ms.contato.avulso.data.domain.UsuarioPerfilAcessoEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.output.MaladiretaVO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import br.com.pacto.ms.contato.avulso.data.repository.EmpresaRepository;
import br.com.pacto.ms.contato.base.data.domain.MaladiretaEntity;
import br.com.pacto.ms.contato.base.data.repository.MalaDiretaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.ConfiguracaoIAException;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.*;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.*;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmFaseIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIAPosVendaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoRedeIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmIAService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.HistoricoPromptService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoCofigRedeEnum;
import br.com.pacto.ms.contato.ia.data.pojo.input.*;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.ApiProxy;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.PactoConversasIAProxy;
import br.com.pacto.ms.contato.ia.service.contract.ContextoService;
import br.com.pacto.ms.contato.ia.service.impl.ContextoServiceImpl;
import br.com.pacto.ms.contato.ia.service.impl.PactoConversasUrlResolverService;
import br.com.pacto.ms.contato.ia.utils.DataSourceUtil;
import br.com.pacto.ms.contato.log.data.repository.UsuarioPerfilAcessoRepository;
import br.com.pacto.ms.contato.log.data.repository.UsuarioRepository;
import br.com.pacto.ms.oamd.domain.OAMDConfiguracaoHorarioPadraoConversasIA;
import br.com.pacto.ms.oamd.repository.OAMDConfiguracaoHorarioPadraoConversasIARepository;
import br.com.pactosolucoes.commons.data.domain.OAMDCompany;
import br.com.pactosolucoes.commons.data.repository.OAMDCompanyRepository;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import br.com.pactosolucoes.utils.PasswordGeneration;
import br.com.pactosolucoes.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.repository.support.JpaRepositoryFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.net.URI;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@SuppressWarnings("unchecked")
@Service
public class ConfiguracaoCrmIAServiceImpl<T, DTO> implements ConfiguracaoCrmIAService<T, DTO> {

    @Value("${custom.async.thread-pool-size:10}")
    private int threadPoolSize;
    private final ExecutorService fixedThreadPool;
    private static final String DEFAULT_USERNAME_PACTO_CONVERSA = "pactoconversas";
    private ModelMapper mapper;
    @Autowired
    private ConfiguracaoCrmFaseIARepository crmFaseIARepository;
    @Autowired
    private ConfiguracaoCrmIARepository configuracaoCrmIARepository;
    @Autowired
    private ConfiguracaoCrmIAPosVendaRepository configuracaoCrmIAPosVendaRepository;
    @Autowired
    private RequestService requestService;
    @Autowired
    private PactoConversasUrlResolverService urlResolverService;
    @Autowired
    private ContextoService contextoService;
    @Autowired
    private PactoConversasIAProxy pactoConversasIAProxy;
    @Autowired
    private MalaDiretaRepository malaDiretaRepository;
    @Autowired
    private UsuarioRepository usuarioRepository;
    @Autowired
    private ApiProxy apiProxy;
    @Autowired
    private UsuarioPerfilAcessoRepository usuarioPerfilAcessoRepository;
    @Autowired
    private ConfiguracaoRedeIARepository configuracaoRedeIARepository;
    @Value("${discovery.url}")
    private String discoveryUrl;
    @Value("${autenticacao.id}")
    String autenticacaoId;
    @Autowired
    private EmpresaRepository empresaRepository;
    @Autowired
    private OAMDCompanyRepository oamdCompanyRepository;
    @Autowired
    private OAMDConfiguracaoHorarioPadraoConversasIARepository oamdConfiguracaoHorarioPadraoConversasIARepository;
    @Autowired
    private HistoricoPromptService historicoPrompService;

    private static final Logger log = LoggerFactory.getLogger(ContextoServiceImpl.class);

    public ConfiguracaoCrmIAServiceImpl(@Value("${custom.async.thread-pool-size:10}") int threadPoolSize) {
        if (threadPoolSize <= 0) {
            threadPoolSize = 10;
        }
        this.threadPoolSize = threadPoolSize;
        this.fixedThreadPool = Executors.newFixedThreadPool(this.threadPoolSize);
    }

    @Override
    public List<ConfiguracaoCrmFaseIADTO> consultar(FasesCRMEnum fase, Integer codigoEmpresa) {
        List<ConfiguracaoCrmFaseIAEntity> entities = crmFaseIARepository.findByFaseAndCodigoEmpresa(fase, codigoEmpresa);
        return entities.isEmpty() ?
                Collections.emptyList() :
                entities.stream().map(ConfiguracaoCrmFaseIADTO::toDto).collect(Collectors.toList());
    }

    //era consultarFases
    @Override
    public List<ConfiguracaoCrmFaseIADTO> consultar(Integer codigoEmpresa) {
        List<ConfiguracaoCrmFaseIAEntity> entities = crmFaseIARepository.findByCodigoEmpresa(codigoEmpresa);
        return entities.stream().map(ConfiguracaoCrmFaseIADTO::toDto).collect(Collectors.toList());
    }

    @Override
    public List<ConfiguracaoCrmFaseIADTO> consultarFases(Integer codigoEmpresa) {
        List<ConfiguracaoCrmFaseIAEntity> configBancoLista = this.crmFaseIARepository.findAllAndCompany(codigoEmpresa);
        List<ConfiguracaoCrmFaseIADTO> crmFaseEnumList = Arrays.stream(FasesCRMEnum.values())
                .map(fase -> ConfiguracaoCrmFaseIADTO.builder()
                        .codigo(fase.getCodigo())
                        .habilitar(false)
                        .fase(fase)
                        .codigometaextra(null)
                        .nomemetaextra(fase.getIdentificador())
                        .codigoEmpresa(null)
                        .build())
                .collect(Collectors.toList());


        for (ConfiguracaoCrmFaseIAEntity config : configBancoLista) {
            if (config.getFase() == null) {
                crmFaseEnumList.add(ConfiguracaoCrmFaseIADTO.builder()
                        .codigo(config.getCodigo())
                        .habilitar(config.getHabilitar())
                        .descricao(config.getDescricao())
                        .mensagensextras(config.getMensagensExtras())
                        .fase(null)
                        .codigometaextra(config.getCodigoMetaExtra())
                        .nomemetaextra(config.getNomeMetaExtra())
                        .codigoEmpresa(config.getCodigoEmpresa())
                        .build());
                continue;
            }

            crmFaseEnumList.stream()
                    .filter(fase -> fase.getFase() != null && Objects.equals(config.getFase().getCodigo(), fase.getFase().getCodigo()))
                    .forEach(fase -> {
                        fase.setHabilitar(config.getHabilitar());
                        fase.setCodigo(config.getCodigo());
                        fase.setDescricao(config.getDescricao());
                        fase.setMensagensextras(config.getMensagensExtras());
                        fase.setCodigometaextra(config.getCodigoMetaExtra());
                        fase.setNomemetaextra(config.getNomeMetaExtra());
                        fase.setCodigoEmpresa(config.getCodigoEmpresa());
                    });
        }
        return crmFaseEnumList;
    }

    @Override
    public ConfiguracaoCrmIADTO consultarConfiguracao(Integer idEmpresa) {
        Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntity = configuracaoCrmIARepository.obterPorCodigoEmpresa(idEmpresa);
        ConfiguracaoRedeIAEntity redeIAEntity = configuracaoRedeIARepository.findAll().stream().findFirst().orElse(new ConfiguracaoRedeIAEntity());
        if (configuracaoCrmIAEntity.isPresent() && configuracaoCrmIAEntity.get().size() > 0) {
            return ConfiguracaoCrmIADTO.toDto(configuracaoCrmIAEntity.get().get(0), redeIAEntity);
        }
        return ConfiguracaoCrmIADTO.toDto(new ConfiguracaoCrmIAEntity(), redeIAEntity);
    }

    @Override
    public T incluirConfiguracaoDeFases(List<ConfiguracaoCrmFaseIADTO> fasesRecebidas, Integer codigoEmpresa) {

        Map<FasesCRMEnum, ConfiguracaoCrmFaseIAEntity> fasesExistentes = crmFaseIARepository.findAllAndCompany(codigoEmpresa).stream()
                .filter(e -> e.getFase() != null)
                .collect(Collectors.toMap(ConfiguracaoCrmFaseIAEntity::getFase, Function.identity()));

        Map<Integer, ConfiguracaoCrmFaseIAEntity> fasesCustomizadasExistentes = crmFaseIARepository.findAllAndCompany(codigoEmpresa).stream()
                .filter(e -> e.getFase() == null && e.getCodigoMetaExtra() != null)
                .collect(Collectors.toMap(ConfiguracaoCrmFaseIAEntity::getCodigoMetaExtra, Function.identity()));

        for (ConfiguracaoCrmFaseIADTO faseDTO : fasesRecebidas) {
            if (faseDTO.getFase() != null) {
                ConfiguracaoCrmFaseIAEntity entidade = fasesExistentes.getOrDefault(faseDTO.getFase(),
                        new ConfiguracaoCrmFaseIAEntity());

                entidade.setFase(faseDTO.getFase());
                entidade.setDescricao(faseDTO.getDescricao());
                entidade.setMensagensExtras(faseDTO.getMensagensextras());
                entidade.setHabilitar(faseDTO.getHabilitar());
                entidade.setCodigoEmpresa(codigoEmpresa);

                crmFaseIARepository.save(entidade);
            } else if (faseDTO.getCodigometaextra() != null) {
                ConfiguracaoCrmFaseIAEntity entidadeCustom = fasesCustomizadasExistentes.getOrDefault(faseDTO.getCodigometaextra(),
                        new ConfiguracaoCrmFaseIAEntity());

                entidadeCustom.setCodigoMetaExtra(faseDTO.getCodigometaextra());
                entidadeCustom.setNomeMetaExtra(faseDTO.getNomemetaextra());
                entidadeCustom.setDescricao(faseDTO.getDescricao());
                entidadeCustom.setMensagensExtras(faseDTO.getMensagensextras());
                entidadeCustom.setHabilitar(faseDTO.getHabilitar());
                entidadeCustom.setCodigoEmpresa(codigoEmpresa);

                crmFaseIARepository.save(entidadeCustom);
            }
        }

        this.atualizarContextoFaseComSeguranca(codigoEmpresa);

        return (T) consultarFases(codigoEmpresa);
    }


    @Deprecated
    @Override
    @Transactional()
    public T incluirFases(List<ConfiguracaoCrmFaseIADTO> configuracoesCrmFaseIADTO, Integer codigoEmpresa) {

        List<ConfiguracaoCrmFaseIADTO> crmFaseIADTOList = Arrays.stream(FasesCRMEnum.values())
                .map(fase -> ConfiguracaoCrmFaseIADTO.builder()
                        .codigo(fase.getCodigo())
                        .descricao(fase.getDescricao())
                        .mensagensextras(null)
                        .habilitar(false)
                        .fase(fase)
                        .codigometaextra(null)
                        .nomemetaextra(fase.getIdentificador())
                        .codigoEmpresa(null)
                        .build())
                .collect(Collectors.toList());


        /**
         * Pega todas salvas no banco por empresa.
         */
        List<ConfiguracaoCrmFaseIAEntity> configBancoLista = this.crmFaseIARepository.findAllAndCompany(codigoEmpresa);


        crmFaseIADTOList.stream()
                .filter(configEnum ->
                        configBancoLista.stream()
                                .noneMatch(configBanco ->
                                        configBanco.getFase() != null && configBanco.getFase().equals(configEnum.getFase())
                                )
                )
                .forEach(configEnum -> {
                    configBancoLista.add(ConfiguracaoCrmFaseIAEntity.builder()
                            .habilitar(configEnum.getHabilitar())
                            .descricao(configEnum.getDescricao())
                            .mensagensExtras(configEnum.getMensagensextras())
                            .fase(configEnum.getFase())
                            .codigoEmpresa(codigoEmpresa)
                            .build());
                });


        /**
         * Pega o que chegou.
         */

        for (ConfiguracaoCrmFaseIADTO configChegouFront : configuracoesCrmFaseIADTO) {

            /**
             * Compara o que chegou com o que tem no banco
             */
            for (ConfiguracaoCrmFaseIAEntity configBanco : configBancoLista) {

                /**
                 * Significa que é uma configuração padrão
                 */
                if (configChegouFront.getFase() != null && configBanco.getFase() != null && configBanco.getFase().equals(configChegouFront.getFase())) {
                    if (configBanco.getCodigo() != null) {
                        configBanco.setHabilitar(configChegouFront.getHabilitar());
                        configBanco.setDescricao(configChegouFront.getDescricao());
                        configBanco.setMensagensExtras(configChegouFront.getMensagensextras());
                        configBanco.setCodigoEmpresa(configChegouFront.getCodigoEmpresa());
                        this.crmFaseIARepository.save(configBanco);
                    } else {
                        this.crmFaseIARepository.save(ConfiguracaoCrmFaseIAEntity.builder()
                                .habilitar(configChegouFront.getHabilitar())
                                .descricao(configChegouFront.getDescricao())
                                .mensagensExtras(configChegouFront.getMensagensextras())
                                .fase(configChegouFront.getFase())
                                .codigoEmpresa(configChegouFront.getCodigoEmpresa())
                                .build());
                        break;
                    }
                }
                /**
                 * Significa que é uma configuração customizada
                 */
                else if (configChegouFront.getFase() == null) {

                    /**
                     * VERIFICAR SE dentro do configBanco TEM ALGUMA COM O configChegouFront.getCodigometaextra()
                     */
                    boolean existsInConfigBanco = configBancoLista.stream()
                            .anyMatch(configBanco2 -> configChegouFront.getCodigometaextra() != null &&
                                    configChegouFront.getCodigometaextra().equals(configBanco2.getCodigoMetaExtra()));

                    if (existsInConfigBanco) {
                        /**
                         * Atualiza uma configuração customizada
                         */
                        configBancoLista.stream()
                                .filter(configBanco3 -> configBanco3.getCodigoMetaExtra() != null &&
                                        configBanco3.getCodigoMetaExtra().equals(configChegouFront.getCodigometaextra()))
                                .findFirst()
                                .ifPresent(configBanco4 -> {
                                    configBanco4.setNomeMetaExtra(configChegouFront.getNomemetaextra());
                                    configBanco4.setHabilitar(configChegouFront.getHabilitar());
                                    configBanco4.setDescricao(configChegouFront.getDescricao());
                                    configBanco4.setMensagensExtras(configChegouFront.getMensagensextras());
                                    configBanco4.setCodigoEmpresa(configChegouFront.getCodigoEmpresa());
                                    this.crmFaseIARepository.save(configBanco4);
                                });
                        break;

                    } else {
                        /**
                         * Cria uma configuração customizada nova
                         */
                        this.crmFaseIARepository.save(ConfiguracaoCrmFaseIAEntity.builder()
                                .habilitar(configChegouFront.getHabilitar())
                                .descricao(configChegouFront.getDescricao())
                                .codigoMetaExtra(configChegouFront.getCodigometaextra())
                                .nomeMetaExtra(configChegouFront.getNomemetaextra())
                                .codigoEmpresa(configChegouFront.getCodigoEmpresa())
                                .codigoEmpresa(codigoEmpresa)
                                .build());
                        break;
                    }
                }
            }
        }


        this.contextoService.atualizarContextoFases(codigoEmpresa);
        return (T) consultarFases(codigoEmpresa);
    }

    @Override
    public void excluir(Integer codigo) {
        ConfiguracaoCrmFaseIAEntity configuracaoCrmIAEntity = this.crmFaseIARepository.findById(codigo)
                .orElseThrow(DataNotMatchException::new);
        this.crmFaseIARepository.delete(configuracaoCrmIAEntity);
    }

    @Deprecated
    @Override
    public T alterar(Integer codigo, ConfiguracaoCrmFaseIADTO configuracaoCrmFaseIADTO) {
        ConfiguracaoCrmFaseIAEntity configuracaoCrmFaseIAEntityDb = this.crmFaseIARepository.findById(codigo)
                .orElseThrow(DataNotMatchException::new);
        ConfiguracaoCrmFaseIAEntity entityNew = mapper.map(configuracaoCrmFaseIADTO, ConfiguracaoCrmFaseIAEntity.class);
        BeanUtils.copyProperties(entityNew, configuracaoCrmFaseIAEntityDb);

        return (T) this.crmFaseIARepository.save(configuracaoCrmFaseIAEntityDb);
    }

    @NotNull
    private ConfiguracaoCrmIAEntity getConfiguracaoCrmIAEntity(ConfiguracaoCrmIADTO configuracaoCrmIADTO) {
        Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntities =
                this.configuracaoCrmIARepository.obterPorCodigoEmpresa(configuracaoCrmIADTO.getCodigoEmpresa());

        if (configuracaoCrmIAEntities.isPresent() && !configuracaoCrmIAEntities.get().isEmpty()) {
            return configuracaoCrmIAEntities.get().get(0);
        } else {
            return new ConfiguracaoCrmIAEntity();
        }
    }

    private void salvarHorarioConfiguracaoIA(ConfiguracaoCrmIADTO configuracaoCrmIADTO) {

        LocalTime horarioAntigo = configuracaoCrmIADTO.getHorarioPadraoAnterior();
        if (horarioAntigo != null) {
            Optional<OAMDConfiguracaoHorarioPadraoConversasIA> oamdConfiguracaoHorarioPadraoConversasIAAntigo = oamdConfiguracaoHorarioPadraoConversasIARepository.buscarPorHora(horarioAntigo);
            List<String> listaDeEmpresasHorarioAntigo = oamdConfiguracaoHorarioPadraoConversasIAAntigo.map(OAMDConfiguracaoHorarioPadraoConversasIA::getChaves).orElse(Collections.emptyList());

            if (!listaDeEmpresasHorarioAntigo.isEmpty() && listaDeEmpresasHorarioAntigo.contains(requestService.getCurrentConfiguration().getCompanyKey())) {
                listaDeEmpresasHorarioAntigo.remove(requestService.getCurrentConfiguration().getCompanyKey());
                oamdConfiguracaoHorarioPadraoConversasIAAntigo.get().setChaves(listaDeEmpresasHorarioAntigo);
                oamdConfiguracaoHorarioPadraoConversasIARepository.save(oamdConfiguracaoHorarioPadraoConversasIAAntigo.get());
            }
        }

        if (configuracaoCrmIADTO.getHorarioPadrao() != null && configuracaoCrmIADTO.isHabilitarconfigia()) {
            Optional<OAMDConfiguracaoHorarioPadraoConversasIA> oamdConfiguracaoHorarioPadraoConversasIAAtual = oamdConfiguracaoHorarioPadraoConversasIARepository.buscarPorHora(configuracaoCrmIADTO.getHorarioPadrao());
            List<String> listaDeAtual = oamdConfiguracaoHorarioPadraoConversasIAAtual.map(OAMDConfiguracaoHorarioPadraoConversasIA::getChaves).orElse(new ArrayList<>());

            if (listaDeAtual.stream().noneMatch(e -> e.equals(requestService.getCurrentConfiguration().getCompanyKey()))) {
                listaDeAtual.add(requestService.getCurrentConfiguration().getCompanyKey());

                if (!oamdConfiguracaoHorarioPadraoConversasIAAtual.isPresent()) {
                    OAMDConfiguracaoHorarioPadraoConversasIA novaConfiguracao = new OAMDConfiguracaoHorarioPadraoConversasIA();
                    novaConfiguracao.setHora(configuracaoCrmIADTO.getHorarioPadrao());
                    novaConfiguracao.setChaves(listaDeAtual);
                    oamdConfiguracaoHorarioPadraoConversasIARepository.save(novaConfiguracao);
                } else {
                    oamdConfiguracaoHorarioPadraoConversasIAAtual.get().setChaves(listaDeAtual);
                    oamdConfiguracaoHorarioPadraoConversasIARepository.save(oamdConfiguracaoHorarioPadraoConversasIAAtual.get());
                }
            }
        }
    }

    @Override
    public T incluirConfiguracaoRede(ConfiguracaoRedeIADTO configuracaoCrmIADTO) {
        ConfiguracaoRedeIAEntity entity = configuracaoRedeIARepository.findAll().stream().findFirst().orElse(new ConfiguracaoRedeIAEntity());
        entity.setChaveBancoMatriz(configuracaoCrmIADTO.getTipoConfigRede().equals(TipoCofigRedeEnum.individual.toString()) ? null : configuracaoCrmIADTO.getChaveBancoMatriz());
        entity.setCodigoUnidadeMatriz(configuracaoCrmIADTO.getTipoConfigRede().equals(TipoCofigRedeEnum.individual.toString()) ? null : configuracaoCrmIADTO.getCodigoUnidadeMatriz());
        entity.setTipoConfigRede(configuracaoCrmIADTO.getTipoConfigRede());
        this.configuracaoRedeIARepository.save(entity);

        return (T) entity;
    }


    @Override
    @ExceptionHandler(ConfiguracaoIAException.class)
    public T incluirPDFConversas(InserirPdfRequestVO inserirPdfRequestVO) {
        if (inserirPdfRequestVO == null || inserirPdfRequestVO.getFile() == null)
         return null;

        try {
            InserirPdfResponseVO inserirPdfResponseVO = this.pactoConversasIAProxy.inserirPDF(URI.create(
                            urlResolverService.getPactoConversasUrl()),
                    identificadorEmpresa(inserirPdfRequestVO.getEmpresaId()),
                    inserirPdfRequestVO);

            return (T) inserirPdfResponseVO;
        } catch (Exception e) {
            throw new ConfiguracaoIAException("Erro ao incluir PDF: " + e.getMessage(), e);
        }
    }

    public String empresaNaRequisicao() {
        return requestService.getCurrentConfiguration().getCompanyKey() + "-" + requestService.getCurrentConfiguration().getCompanyId();
    }

    public String identificadorEmpresa(Integer empresa) {
        return requestService.getCurrentConfiguration().getCompanyKey() + "-" + empresa;
    }

    @Override
    public T consultarPDFConversas(Integer empresa) {
        if (empresa == null) {
            throw new IllegalArgumentException("Empresa não pode ser nula");
        }

        try {
            ResponsePDFDocumentoVO responsePDFDocumentoVO =
                    this.pactoConversasIAProxy.consultarPDF(URI.create(
                                    urlResolverService.getPactoConversasUrl()),
                            identificadorEmpresa(empresa));

            return (T) responsePDFDocumentoVO;
        } catch (Exception e) {
            log.error("Erro ao consultar PDF", e);
            return (T) new ResponsePDFDocumentoVO(new DocumentoPDFDataVO("error"));
        }

    }

    @Override
    public T deletarPDF(Integer empresa) {
        if (empresa == null)
            throw new IllegalArgumentException("Empresa não pode ser nula");

        try {
            InserirPdfResponseVO responseVO = this.pactoConversasIAProxy.deletarPDF(URI.create(
                            urlResolverService.getPactoConversasUrl()),
                    identificadorEmpresa(empresa));
            return (T) responseVO;
        } catch (FeignException e) {
            log.error("Erro ao deletar PDF", e);
            throw new ConfiguracaoIAException("Erro ao deletar PDF: " + e);
        }
    }

    @Override
    @ExceptionHandler(Exception.class)
    public T incluirConfiguracao(ConfiguracaoCrmIADTO configuracaoCrmIADTO) {
        try {
            ConfiguracaoCrmIAEntity entity = getConfiguracaoCrmIAEntity(configuracaoCrmIADTO);

            ConfiguracaoCrmIAEntity configuracaoAnterior = null;
            if (entity.getCodigo() != null) {
                configuracaoAnterior = clonarConfiguracaoParaHistorico(entity);
            }

            UsuarioDTO usuarioDTO = buscaOuCriaUsuarioPactoConversaIA(configuracaoCrmIADTO.getCodigoEmpresa());
            popularEntidadeConfiguracaoCrm(configuracaoCrmIADTO, entity, usuarioDTO);
            salvarHorarioConfiguracaoIA(configuracaoCrmIADTO);

            ConfiguracaoRedeIAEntity configuracaoRedeIAEntity = configuracaoRedeIARepository.findAll()
                    .stream()
                    .findFirst()
                    .orElse(new ConfiguracaoRedeIAEntity());

            boolean isMatriz = verificarSeMatrizRede(configuracaoCrmIADTO, configuracaoRedeIAEntity);
            boolean rede = TipoCofigRedeEnum.rede.toString().equals(configuracaoRedeIAEntity.getTipoConfigRede()) || configuracaoCrmIADTO.getRede();

            entity.setMatriz(isMatriz);
            entity = this.configuracaoCrmIARepository.save(entity);

            // Salvar histórico comparando com a configuração anterior
            historicoPrompService.salvarHistoricoPromptsConfiguracao(entity, configuracaoAnterior);

            String chaveMatriz = montarChaveMatriz(configuracaoCrmIADTO, configuracaoRedeIAEntity);
            atualizarContextos(entity, isMatriz, rede, chaveMatriz);

            return (T) entity;
        } catch (Exception e) {
            log.error("Erro ao salvar configuração IA", e);
            return null;
        }
    }

    private String montarChaveMatriz(ConfiguracaoCrmIADTO dto, ConfiguracaoRedeIAEntity entity) {
        if (dto.getChaveMatriz() != null && dto.getUnidadeMatriz() != null) {
            return dto.getChaveMatriz() + "-" + dto.getUnidadeMatriz();
        } else if (entity.getChaveBancoMatriz() != null && entity.getCodigoUnidadeMatriz() != null) {
            return entity.getChaveBancoMatriz() + "-" + entity.getCodigoUnidadeMatriz();
        }
        return null;
    }

    private void atualizarContextos(ConfiguracaoCrmIAEntity entity, boolean isMatriz, boolean rede, String chaveMatriz) {
        try {
            this.contextoService.atualizarContextoEmpresa(entity.getCodigoEmpresa(), isMatriz, rede, chaveMatriz);
            this.contextoService.atualizarContextoPersonalidade(entity.getCodigoEmpresa());
            this.contextoService.atualizarContextoProdutos(entity.getCodigoEmpresa());
            this.contextoService.atualizarContextoTurmas(entity.getCodigoEmpresa());
            this.contextoService.atualizarContextoPlanos(entity.getCodigoEmpresa());
        } catch (Exception e) {
            log.error("Erro ao atualizar contextos da empresa: {}", entity.getCodigoEmpresa(), e);
            e.printStackTrace();
        }
    }

    private void atualizarContextoFaseComSeguranca(Integer empresa) {
        try {
            this.contextoService.atualizarContextoFases(empresa);
        } catch (Exception e) {
            log.error("Erro ao atualizar contextos da empresa: {}", empresa, e);
            e.printStackTrace();
        }
    }

    private ConfiguracaoCrmIAEntity clonarConfiguracaoParaHistorico(ConfiguracaoCrmIAEntity original) {
        ConfiguracaoCrmIAEntity clone = new ConfiguracaoCrmIAEntity();
        clone.setCodigo(original.getCodigo());
        clone.setCodigoEmpresa(original.getCodigoEmpresa());

        clone.setPersonalidade(original.getPersonalidade());
        clone.setInformacoesAdicionaisAcademia(original.getInformacoesAdicionaisAcademia());
        return clone;
    }

    private static void popularEntidadeConfiguracaoCrm(ConfiguracaoCrmIADTO configuracaoCrmIADTO, ConfiguracaoCrmIAEntity entity, UsuarioDTO usuarioDTO) {
        entity.setCodigoEmpresa(configuracaoCrmIADTO.getCodigoEmpresa());

        entity.setHabilitarconfigia(configuracaoCrmIADTO.isHabilitarconfigia());
        entity.setPersonalidade(configuracaoCrmIADTO.getPersonalidade() != null ? configuracaoCrmIADTO.getPersonalidade() : entity.getPersonalidade());
        entity.setInformacoesAdicionaisAcademia(configuracaoCrmIADTO.getInformacoesAdicionaisAcademia() != null ? configuracaoCrmIADTO.getInformacoesAdicionaisAcademia() : entity.getInformacoesAdicionaisAcademia());
        entity.setWhatsappBusiness(!Objects.isNull(configuracaoCrmIADTO.getWhatsappBusiness()) && configuracaoCrmIADTO.getWhatsappBusiness());

        String username = configuracaoCrmIADTO.getLoginPactoConversas() != null && !configuracaoCrmIADTO.getLoginPactoConversas().isEmpty()
                ? configuracaoCrmIADTO.getLoginPactoConversas()
                : usuarioDTO.getUsername();
        String senha = configuracaoCrmIADTO.getSenhaPactoConversas() != null && !configuracaoCrmIADTO.getSenhaPactoConversas().isEmpty()
                ? configuracaoCrmIADTO.getSenhaPactoConversas()
                : usuarioDTO.getSenha();
        entity.setLoginPactoConversas(username);
        entity.setSenhaPactoConversas(senha);

        entity.setHorarioPadrao(configuracaoCrmIADTO.getHorarioPadrao());
        entity.setTokenZApi(configuracaoCrmIADTO.getTokenZApi());
        entity.setIdInstanciaZApi(configuracaoCrmIADTO.getIdInstanciaZApi() == null ? entity.getIdInstanciaZApi() : configuracaoCrmIADTO.getIdInstanciaZApi());
        entity.setEmailResponsavelConversasAI(configuracaoCrmIADTO.getEmailResponsavelConversasAI());
        entity.setTelefoneResponsavelConversasAI(configuracaoCrmIADTO.getTelefoneResponsavelConversasAI());
        entity.setDesabilitarAgendamentoAulasExperimentais(configuracaoCrmIADTO.getDesabilitarAgendamentoAulasExperimentais());

        if (Objects.nonNull(configuracaoCrmIADTO.getConfiguracaoGymbot())) {
            ConfiguracaoGymbot configuracaoGymbot = new ConfiguracaoGymbot();
            ConfiguracaoGymbotDTO dto = configuracaoCrmIADTO.getConfiguracaoGymbot();
            configuracaoGymbot.setHabilitarGymbot(dto.getHabilitarGymbot());
            configuracaoGymbot.setTokenGymbot(dto.getTokenGymbot());
            configuracaoGymbot.setDescricaoDepartamento(dto.getDescricaoDepartamento());
            configuracaoGymbot.setIdDepartamento(dto.getIdDepartamento());
            entity.setConfiguracaoGymbot(configuracaoGymbot);
        }
    }

    private boolean verificarSeMatrizRede(ConfiguracaoCrmIADTO configuracaoCrmIADTO, ConfiguracaoRedeIAEntity redeIAEntity) {
        return redeIAEntity.getTipoConfigRede() != null &&
                redeIAEntity.getTipoConfigRede().equals(TipoCofigRedeEnum.rede.toString()) &&
                redeIAEntity.getChaveBancoMatriz() != null &&
                redeIAEntity.getCodigoUnidadeMatriz() != null &&
                redeIAEntity.getChaveBancoMatriz().equals(requestService.getCurrentConfiguration().getCompanyKey()) &&
                redeIAEntity.getCodigoUnidadeMatriz().equals(configuracaoCrmIADTO.getCodigoEmpresa());
    }

    public void enviarParaTodasAcademiasDaRede(ConfiguracaoCrmIADTO configuracaoCrmIADTO) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        configuracaoCrmIADTO.setMatriz(false);
        configuracaoCrmIADTO.setCodigo(null);
        configuracaoCrmIADTO.setUnidadeMatriz(requestService.getCurrentConfiguration().getCompanyId());
        configuracaoCrmIADTO.setChaveMatriz(requestService.getCurrentConfiguration().getCompanyKey());
        Optional<EmpresaEntity> empresa = this.empresaRepository.findById(configuracaoCrmIADTO.getCodigoEmpresa());
        empresa.ifPresent(empresaEntity -> configuracaoCrmIADTO.setNomeMatriz(empresaEntity.getNome()));

        log.info("Entrou no metodo enviarParaTodasAcademiasDaRede com esse DTO -------------------------------------- {}", configuracaoCrmIADTO);

        prepararEnvioEmpresaMultBanco(configuracaoCrmIADTO, futures);

        envioBancoMultEmpresa(configuracaoCrmIADTO);

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> System.out.println("Configurações propagadas para todas as academias.")).join();
    }

    private void envioBancoMultEmpresa(ConfiguracaoCrmIADTO configuracaoCrmIADTO) {
        List<ResumoEmpresaVO> empresaUnidades = this.empresaRepository.consultarEmpresasAtivas();

        // Remove a própria matriz da lista
        empresaUnidades.remove(empresaUnidades.stream()
                .filter(unidade -> unidade.getCodigo().equals(configuracaoCrmIADTO.getCodigoEmpresa()))
                .findFirst().orElse(null));

        log.info("Lista de Banco Mult Banco UNIDADE que vão ser enviadas  -------------------------------------- {}", empresaUnidades);


        for (ResumoEmpresaVO empresaUnidade : empresaUnidades) {
            if (empresaUnidade.getCodigo() != null)
                configuracaoCrmIADTO.setCodigoEmpresa(empresaUnidade.getCodigo());
            this.incluirConfiguracao(configuracaoCrmIADTO);
        }
    }

    private void prepararEnvioEmpresaMultBanco(ConfiguracaoCrmIADTO configuracaoCrmIADTO, List<CompletableFuture<Void>> futures) {
        DiscoveryBuscarRedeDTO discoveryBuscarRedeDTO = this.apiProxy.mandarGet(
                discoveryUrl + "/find/rede/" + requestService.getCurrentConfiguration().getCompanyKey(),
                null,
                DiscoveryBuscarRedeDTO.class);

        // Remove a própria matriz da lista
        discoveryBuscarRedeDTO.getContent().remove(
                discoveryBuscarRedeDTO.getContent().stream()
                        .filter(empresaRede -> empresaRede.getChave().equals(requestService.getCurrentConfiguration().getCompanyKey()) ||
                                empresaRede.getChave() == null)
                        .findFirst().orElse(null)
        );

        log.info("Lista de Empresa Mult Banco que vão ser enviadas  -------------------------------------- {}", discoveryBuscarRedeDTO);


        futures.addAll(discoveryBuscarRedeDTO.getContent().stream()
                .map(empresaDaMesmaRede -> processarAcademia(empresaDaMesmaRede.getChave(), configuracaoCrmIADTO, null))
                .collect(Collectors.toList()));
    }


    public CompletableFuture<Void> processarAcademia(String chaveEmpresa, ConfiguracaoCrmIADTO configuracaoCrmIADTO, Integer unidade) {
        return CompletableFuture.runAsync(() -> {
            DiscoveryBuscarDTO urlsDaAcademia = this.apiProxy.mandarGet(
                    discoveryUrl + "/find/" + chaveEmpresa,
                    null,
                    DiscoveryBuscarDTO.class);

            if (unidade != null)
                configuracaoCrmIADTO.setCodigoEmpresa(unidade);

            AutenticacaoDTO autenticacaoMsContentResponseVO = getAutenticacaoMsV2ContentResponseVO(chaveEmpresa, urlsDaAcademia.getContent().getServiceUrls().getAutenticacaoUrl());

            log.info("Respose do autenticacaoMsContentResponseVO   -------------------------------------- {}", autenticacaoMsContentResponseVO);


            Object response = this.apiProxy.mandarPost(
                    null,
                    urlsDaAcademia.getContent().getServiceUrls().getContatoMsUrl() + "/v1/configuracao/ia/incluir",
                    autenticacaoMsContentResponseVO.getContent(),
                    configuracaoCrmIADTO
            );

            log.info("Respose do processarAcademia   -------------------------------------- {}", response);

        }, fixedThreadPool);
    }

    private AutenticacaoDTO getAutenticacaoMsV2ContentResponseVO(String company, String baseUrl) {

        return this.apiProxy.mandarPost(baseUrl + "/aut/v2/gt", null, PersonaDTO
                .builder()
                .chave(company)
                .id(autenticacaoId)
                .build(), AutenticacaoDTO.class);
    }

    private UsuarioDTO buscaOuCriaUsuarioPactoConversaIA(Integer empresaId) {
        Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntities =
                this.configuracaoCrmIARepository.obterPorCodigoEmpresa(empresaId);
        if (configuracaoCrmIAEntities.isPresent() && !configuracaoCrmIAEntities.get().isEmpty() && configuracaoCrmIAEntities.get().get(0).getLoginPactoConversas() != null && configuracaoCrmIAEntities.get().get(0).getSenhaPactoConversas() != null) {
            return UsuarioDTO.builder()
                    .username(configuracaoCrmIAEntities.get().get(0).getLoginPactoConversas())
                    .senha(configuracaoCrmIAEntities.get().get(0).getSenhaPactoConversas())
                    .build();
        } else {
            Optional<List<ConfiguracaoCrmIAEntity>> matriz =
                    this.configuracaoCrmIARepository.obterMatriz();
            if (matriz.isPresent() && !configuracaoCrmIAEntities.get().isEmpty() && !matriz.get().isEmpty() && matriz.get().get(0).getLoginPactoConversas() != null && matriz.get().get(0).getSenhaPactoConversas() != null) {
                return UsuarioDTO.builder()
                        .username(matriz.get().get(0).getLoginPactoConversas())
                        .senha(matriz.get().get(0).getSenhaPactoConversas())
                        .build();
            }
        }

        Optional<List<ConfiguracaoCrmIAEntity>> lista = this.configuracaoCrmIARepository.findAllOptional();
        if (lista.isPresent() && !lista.get().isEmpty()) {
            List<ConfiguracaoCrmIAEntity> listaConfigsValidas = lista.get().stream()
                    .filter(atual -> atual.getLoginPactoConversas() != null && atual.getSenhaPactoConversas() != null && atual.getLoginPactoConversas().equals(DEFAULT_USERNAME_PACTO_CONVERSA))
                    .collect(Collectors.toList());

            if (!listaConfigsValidas.isEmpty()) {
                Integer codigoUsuario = this.usuarioRepository.getCodigoUsuario(DEFAULT_USERNAME_PACTO_CONVERSA);
                Optional<UsuarioPerfilAcessoEntity> empresaAtual =
                        this.usuarioPerfilAcessoRepository.verificaSePossueAcessoAEmpresa(empresaId, codigoUsuario);
                Optional<List<UsuarioPerfilAcessoEntity>> outraEmpresaMesmoBanco =
                        this.usuarioPerfilAcessoRepository.buscarPerfilAcessoOutraEmpresa(codigoUsuario);

                if (!empresaAtual.isPresent()) {
                    this.usuarioPerfilAcessoRepository.save(UsuarioPerfilAcessoEntity.builder()
                            .empresa(empresaId)
                            .usuario(outraEmpresaMesmoBanco.get().get(0).getUsuario())
                            .perfilAcesso(outraEmpresaMesmoBanco.get().get(0).getPerfilAcesso())
                            .unificado(outraEmpresaMesmoBanco.get().get(0).getUnificado())
                            .build());
                }

                return UsuarioDTO.builder()
                        .username(listaConfigsValidas.stream().findFirst().get().getLoginPactoConversas())
                        .senha(listaConfigsValidas.stream().findFirst().get().getSenhaPactoConversas())
                        .build();
            }

        }

        String senha = PasswordGeneration.generatePassword(6);
        UsuarioDTO usuarioDTO = UsuarioDTO.builder()
                .username(DEFAULT_USERNAME_PACTO_CONVERSA)
                .nome("Conversas AI")
                .senha(senha)
                .senhaConfirmar(senha)
                .pin(PasswordGeneration.generatePin(3))
                .email("<EMAIL>")
                .build();
        apiProxy.mandarPost(requestService.getClienteDiscovery().getServiceUrls().getZwUrl() + "/user/pactoConversa/add?chave=" + requestService.getCurrentConfiguration().getCompanyKey() + "&empresa=" + empresaId, null, usuarioDTO);
        UsuarioEntity UsuarioPactoConversaNovo = usuarioRepository.getUsuarioPactoConversa(DEFAULT_USERNAME_PACTO_CONVERSA);
        if (UsuarioPactoConversaNovo == null) {
            throw new RuntimeException("Usuario Pacto Conversa não encontrado");
        }
        return usuarioDTO;
    }

    @Override
    public List<ConfiguracaoCrmIAPosVendaDTO> consultarConfigPosVenda() {
        return configuracaoCrmIAPosVendaRepository.findAll().stream().map(ConfiguracaoCrmIAPosVendaDTO::toDto).collect(Collectors.toList());
    }

    @Override
    @Deprecated()
    public T incluirPosVenda(List<ConfiguracaoCrmIAPosVendaDTO> configuracaoCrmIAPosVendaDTOs) {
        List<ConfiguracaoCrmIAPosVendaEntity> posVendaEntities = configuracaoCrmIAPosVendaRepository.findAll();
        if (posVendaEntities.isEmpty()) {
            posVendaEntities = configuracaoCrmIAPosVendaDTOs.stream()
                    .map(dto -> mapper.map(dto, ConfiguracaoCrmIAPosVendaEntity.class))
                    .collect(Collectors.toList());

            configuracaoCrmIAPosVendaRepository.saveAll(posVendaEntities);
            return (T) posVendaEntities;
        }
        for (ConfiguracaoCrmIAPosVendaDTO dto : configuracaoCrmIAPosVendaDTOs) {
            ConfiguracaoCrmIAPosVendaEntity entity = posVendaEntities.stream()
                    .filter(e -> e.getCodigoposvenda().equals(dto.getCodigoposvenda()))
                    .findFirst()
                    .orElse(new ConfiguracaoCrmIAPosVendaEntity());

            mapper.map(dto, entity);
            configuracaoCrmIAPosVendaRepository.save(entity);
        }
        return (T) configuracaoCrmIAPosVendaRepository.findAll();
    }

    public T obterQrCode(String instancia, String token) {
        try {
            String qrCodeResponse = pactoConversasIAProxy.obterQrCode(URI.create(urlResolverService.getPactoConversasUrl()), instancia, token);
            HashMap<String, Object> data = parseQrCodeResponse(qrCodeResponse);
            QrCodeVO qrCodeVO = criarQrCodeVO(data);
            return (T) qrCodeVO;
        } catch (FeignException e) {
            if (400 == e.status()) {
                boolean statusInstancia = verificarStatusInstancia(instancia, token);
                if (!statusInstancia) {
                    InstanciaVO instanciaVO = (InstanciaVO) subscreverInstancia(instancia, token);
                    String qrCodeResponse = pactoConversasIAProxy.obterQrCode(URI.create(urlResolverService.getPactoConversasUrl()), instanciaVO.getId(), instanciaVO.getToken());
                    HashMap<String, Object> data = parseQrCodeResponse(qrCodeResponse);
                    QrCodeVO qrCodeVO = criarQrCodeVO(data);
                    return (T) qrCodeVO;
                }
            }
            log.error("Erro ao obter o QR Code para a instância {} no ambiente {}.", instancia, e);
            throw new ConfiguracaoIAException("Não foi possível obter o QR Code. Por favor, tente novamente mais tarde.", e);
        }
    }

    private HashMap<String, Object> parseQrCodeResponse(String response) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(response, HashMap.class);
        } catch (Exception e) {
            throw new ConfiguracaoIAException("Erro ao processar a resposta do QR Code.", e);
        }
    }

    private QrCodeVO criarQrCodeVO(HashMap<String, Object> data) {
        QrCodeVO qrCodeVO = new QrCodeVO();
        Optional.ofNullable(data.get("value"))
                .filter(value -> value.toString().startsWith("data:image"))
                .ifPresent(value -> {
                    qrCodeVO.setQrCode(true);
                    qrCodeVO.setImageContent(value.toString());
                    qrCodeVO.setStatus("not_connected");
                });
        Optional.ofNullable(data.get("connected"))
                .ifPresent(value -> {
                    qrCodeVO.setQrCode(false);
                    qrCodeVO.setStatus("connected");
                });
        return qrCodeVO;
    }

    @Override
    public T desconectarInstancia(Integer codigoEmpresa) {
        Optional<List<ConfiguracaoCrmIAEntity>> optionalConfiguracoes =
                configuracaoCrmIARepository.findByCodigoEmpresa(codigoEmpresa);

        if (!optionalConfiguracoes.isPresent() || optionalConfiguracoes.get().isEmpty()) {
            throw new ConfiguracaoIAException("Nenhuma configuração encontrada para a empresa.");
        }

        ConfiguracaoCrmIAEntity entity = optionalConfiguracoes.get().get(0);
        String instancia = entity.getIdInstanciaZApi();
        String token = entity.getTokenZApi();

        String url = urlResolverService.getPactoConversasUrl();

        String response = pactoConversasIAProxy.desconectarInstancia(
                URI.create(url),
                instancia,
                token
        );

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            HashMap<String, Boolean> jsonResponse = objectMapper.readValue(response, HashMap.class);
            InstanciaVO instanciaVO = new InstanciaVO();
            instanciaVO.setId(instancia);
            instanciaVO.setToken(token);
            instanciaVO.setValue(jsonResponse.get("value"));
            return (T) instanciaVO;
        } catch (Exception e) {
            log.error("Erro ao desconectar instancia no ambiente {}.", e);
            throw new ConfiguracaoIAException("Erro ao desconectar instância no ambiente", e);
        }
    }

    @Override
    public T criarInstancia(ConfiguracaoInstanciaDTO configuracaoInstanciaDTO) {

        HashMap<String, Object> map = new HashMap<>();
        map.put("name", configuracaoInstanciaDTO.getNomeEmpresa());
        map.put("businessDevice", configuracaoInstanciaDTO.getWhatsappBusiness());
        map.put("isDevice", configuracaoInstanciaDTO.getIsDevice());
        map.put("receivedCallbackUrl", "");

        try {
            Optional<List<ConfiguracaoCrmIAEntity>> byCodigoEmpresa = configuracaoCrmIARepository.findByCodigoEmpresa(configuracaoInstanciaDTO.getCodigoEmpresa());


            if (!configuracaoInstanciaDTO.getFlagNovaInstancia() && byCodigoEmpresa.isPresent() && !byCodigoEmpresa.get().isEmpty()) {
                ConfiguracaoCrmIAEntity configuracao = byCodigoEmpresa.get().get(0);

                if (existeInstancia(configuracaoInstanciaDTO, configuracao)) {
                    if (!verificarStatusInstancia(configuracao.getIdInstanciaZApi(), configuracao.getTokenZApi())) {
                        subscreverInstancia(configuracao.getIdInstanciaZApi(), configuracao.getTokenZApi());
                    }

                    return (T) InstanciaVO.builder()
                            .id(configuracao.getIdInstanciaZApi())
                            .token(configuracao.getTokenZApi())
                            .build();
                }
            }

            InstanciaVO instanciaVO = obterNovaInstanciaConvesasIA(map);

            ConfiguracaoCrmIAEntity entity = new ConfiguracaoCrmIAEntity();

            if (byCodigoEmpresa.isPresent() && !byCodigoEmpresa.get().isEmpty())
                entity = byCodigoEmpresa.get().get(0);

            entity.setHabilitarconfigia(true);
            entity.setIdInstanciaZApi(instanciaVO.getId());
            entity.setTokenZApi(instanciaVO.getToken());
            entity.setWhatsappBusiness(configuracaoInstanciaDTO.getWhatsappBusiness());
            entity.setCodigoEmpresa(configuracaoInstanciaDTO.getCodigoEmpresa());
            configuracaoCrmIARepository.save(entity);

            return (T) instanciaVO;
        } catch (Exception e) {
            log.error("Erro ao criar instância {}.", e);
            throw new ConfiguracaoIAException("Erro ao criar instância", e);
        }
    }

    @NotNull
    private InstanciaVO obterNovaInstanciaConvesasIA(HashMap<String, Object> map) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        HashMap<String, String> jsonResponse = objectMapper.readValue(
                pactoConversasIAProxy.criarInstancia(URI.create(urlResolverService.getPactoConversasUrl()), map),
                HashMap.class);

        InstanciaVO instanciaVO = new InstanciaVO();
        instanciaVO.setId(jsonResponse.get("id"));
        instanciaVO.setToken(jsonResponse.get("token"));
        return instanciaVO;
    }

    private static boolean existeInstancia(ConfiguracaoInstanciaDTO configuracaoInstanciaDTO, ConfiguracaoCrmIAEntity configuracao) {
        return StringUtils.isNotBlank(configuracao.getTokenZApi())
                && StringUtils.isNotBlank(configuracao.getIdInstanciaZApi())
                && configuracaoInstanciaDTO.getWhatsappBusiness().equals(configuracao.getWhatsappBusiness());
    }

    @Override
    public Boolean verificarStatusInstancia(String instanciaId, String token) {
        try {
            HashMap<String, Object> jsonResponse = parseJsonResponse(
                    pactoConversasIAProxy.checkarStatusInstancia(URI.create(urlResolverService.getPactoConversasUrl()), instanciaId, token)
            );
            return isPhoneConnected(jsonResponse) && instanceIsConnected(jsonResponse);
        } catch (FeignException e) {
            if (e.status() == HttpStatus.BAD_REQUEST.value()) {
                return Boolean.FALSE;
            }
            throw new ConfiguracaoIAException(e.getMessage());
        } catch (Exception e) {
            log.error("Erro ao verificar status da instância {}.", e);
            throw new ConfiguracaoIAException(e.getMessage());
        }
    }

    private HashMap<String, Object> parseJsonResponse(String response) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(response, HashMap.class);
        } catch (JsonProcessingException e) {
            throw new ConfiguracaoIAException("Erro ao processar a resposta JSON.", e);
        }
    }

    private boolean isPhoneConnected(HashMap<String, Object> jsonResponse) {
        Object smartphoneConnected = jsonResponse.get("smartphoneConnected");
        return smartphoneConnected instanceof Boolean && (Boolean) smartphoneConnected;
    }

    private boolean instanceIsConnected(HashMap<String, Object> jsonResponse) {
        Object connected = jsonResponse.get("connected");
        return connected instanceof Boolean && (Boolean) connected;
    }

    @Override
    public T subscreverInstancia(String instancia, String token) {
        try {
            String response = pactoConversasIAProxy.subscreverInstancia(URI.create(urlResolverService.getPactoConversasUrl()), instancia, token);
            ObjectMapper objectMapper = new ObjectMapper();
            HashMap<String, Boolean> jsonResponse = objectMapper.readValue(response, HashMap.class);
            InstanciaVO instanciaVO = new InstanciaVO();
            instanciaVO.setId(instancia);
            instanciaVO.setToken(token);
            instanciaVO.setValue(jsonResponse.get("value"));
            return (T) instanciaVO;
        } catch (Exception e) {
            log.error("Erro ao subscrever instância {}.", e);
            throw new ConfiguracaoIAException("Erro ao subscrever instância ", e);
        }
    }

    @Override
    public T cancelarInstancia(String instancia, String token, Integer codigoEmpresa) {

        try {
            String response = pactoConversasIAProxy.cancelarInstancia(URI.create(urlResolverService.getPactoConversasUrl()), instancia, token);
            ObjectMapper objectMapper = new ObjectMapper();
            HashMap<String, Boolean> jsonResponse = objectMapper.readValue(response, HashMap.class);
            InstanciaVO instanciaVO = new InstanciaVO();
            instanciaVO.setId(instancia);
            instanciaVO.setToken(token);
            instanciaVO.setValue(jsonResponse.get("value"));

            removerInstanciaDaConfiguracao(codigoEmpresa);

            return (T) instanciaVO;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ConfiguracaoIAException("Erro ao cancelar instancia", e);
        }
    }

    private void removerInstanciaDaConfiguracao(Integer codigoEmpresa) {
        Optional<List<ConfiguracaoCrmIAEntity>> byCodigoEmpresa = configuracaoCrmIARepository.findByCodigoEmpresa(codigoEmpresa);

        if (byCodigoEmpresa.isPresent() && !byCodigoEmpresa.get().isEmpty()) {
            ConfiguracaoCrmIAEntity entity = byCodigoEmpresa.get().get(0);
            entity.setIdInstanciaZApi(null);
            entity.setTokenZApi(null);
            entity.setWhatsappBusiness(false);
            configuracaoCrmIARepository.save(entity);
        }
    }

    @Override
    public T obterInstanciaEmpresa(String chaveEmpresa) {
        try {
            String response = pactoConversasIAProxy.obterInstanciaEmpresa(
                    URI.create(urlResolverService.getPactoConversasUrl()), chaveEmpresa);
            ObjectMapper objectMapper = new ObjectMapper();
            HashMap<String, Object> jsonResponse = objectMapper.readValue(response, HashMap.class);
            InstanciaVO instanciaVO = new InstanciaVO();
            instanciaVO.setId((String) jsonResponse.get("instance_id"));
            instanciaVO.setToken((String) jsonResponse.get("token"));
            instanciaVO.setValue((Boolean) jsonResponse.get("value"));
            return (T) instanciaVO;
        } catch (FeignException e) {
            if (400 == e.status()) {
                log.error("Empresa nao encontrada");
                return (T) InstanciaVO.builder().value(false).build();
            }
            throw new ConfiguracaoIAException("Erro ao obter instancia empresa", e);
        } catch (Exception e) {
            throw new ConfiguracaoIAException("Erro ao obter instancia empresa", e);
        }
    }

    @Override
    public List<ResumoEmpresaVO> buscarUnidadesDaEmpresaPorBanco(String empresa) {
        Optional<OAMDCompany> optionalOAMDCompany = this.oamdCompanyRepository.findById(empresa);
        if (!optionalOAMDCompany.isPresent()) {
            return new ArrayList<>();
        }

        OAMDCompany oamdCompany = optionalOAMDCompany.get();

        try {
            DataSource dataSource = DataSourceUtil.createDataSource(oamdCompany);
            List<ResumoEmpresaVO> resultado = new ArrayList<>();

            DataSourceUtil.executeWithDynamicDataSource(dataSource, () -> {
                EntityManager em = DataSourceUtil.getEntityManager(dataSource,
                        "br.com.pacto.ms.contato.avulso.data.domain",
                        "br.com.pacto.ms.contato.base.data.domain");
                JpaRepositoryFactory factory = new JpaRepositoryFactory(em);

                EmpresaRepository dynamicEmpresaRepository = factory.getRepository(EmpresaRepository.class);
                resultado.addAll(dynamicEmpresaRepository.consultarEmpresasAtivas());
            });

            return resultado;

        } catch (Exception e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    @Override
    public List<MaladiretaVO> consultarFaseExtrasIA(Integer codigoEmpresa) {
        List<MaladiretaVO> maladiretaVO = new ArrayList<>();
        String username = Optional.ofNullable(this.consultarConfiguracao(codigoEmpresa))
                .map(ConfiguracaoCrmIADTO::getLoginPactoConversas)
                .orElse(null);

        if (username == null)
            return maladiretaVO;


        List<MaladiretaEntity> maladiretaEntityList = malaDiretaRepository.findByUsuario(username);
        return maladiretaEntityList.isEmpty()
                ? maladiretaVO
                : maladiretaEntityList.stream()
                .map(MaladiretaVO::toVO)
                .collect(Collectors.toList());
    }

    public StatusInstanceVO obterInformacoesDispositivoConectado(String instanceId, String token) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            boolean statusInstancia = verificarStatusInstancia(instanceId, token);
            if(!statusInstancia) {
                return StatusInstanceVO.builder().build();
            }

            JsonNode responseJson = objectMapper.readTree(
                    pactoConversasIAProxy.getDevice(URI.create(urlResolverService.getPactoConversasUrl()), instanceId, token));

            return StatusInstanceVO.builder()
                    .phone(Objects.isNull(responseJson.get("phone")) ? "" : responseJson.get("phone").asText())
                    .name(Objects.isNull(responseJson.get("name")) ? "" : responseJson.get("name").asText())
                    .imgUrl(Objects.isNull(responseJson.get("imgUrl")) ? "" : responseJson.get("imgUrl").asText())
                    .originalDevice(Objects.isNull(responseJson.get("originalDevice")) ? "" : responseJson.get("originalDevice").asText())
                    .build();
        } catch (JsonProcessingException e) {
            log.error("Msg: Erro ao obter informações dos dispositivos conectados: ", e.getMessage());
            return null;
        }
    }
}
